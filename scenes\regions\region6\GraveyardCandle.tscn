[gd_scene load_steps=8 format=3 uid="uid://ocpnbejvu51t"]

[ext_resource type="Texture2D" uid="uid://bndjqyjofeei7" path="res://resources/exterior/Graveyard_Candle_Off2.png" id="1_3ayf4"]
[ext_resource type="Texture2D" uid="uid://cqjs7g0rnsfhf" path="res://resources/exterior/Graveyard_Candle_Standing_16x16_2.png" id="2_iwcyb"]

[sub_resource type="Animation" id="Animation_saf3j"]
resource_name = "Off"
length = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Off:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Animating:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}

[sub_resource type="Animation" id="Animation_80t8h"]
resource_name = "On"
length = 0.4
loop_mode = 1
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Animating:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Off:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Animating:frame")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 1,
"values": [0, 1, 2, 3]
}

[sub_resource type="Animation" id="Animation_uubtr"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Off:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Animating:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Animating:frame")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [0]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_3ayf4"]
_data = {
&"Off": SubResource("Animation_saf3j"),
&"On": SubResource("Animation_80t8h"),
&"RESET": SubResource("Animation_uubtr")
}

[sub_resource type="CircleShape2D" id="CircleShape2D_3ayf4"]
radius = 12.0

[node name="GraveyardCandle" type="Node2D"]

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_3ayf4")
}
speed_scale = 0.7

[node name="Off" type="Sprite2D" parent="."]
texture = ExtResource("1_3ayf4")

[node name="Animating" type="Sprite2D" parent="."]
visible = false
texture = ExtResource("2_iwcyb")
hframes = 4

[node name="StaticBody2D" type="StaticBody2D" parent="."]

[node name="CollisionPolygon2D" type="CollisionPolygon2D" parent="StaticBody2D"]
polygon = PackedVector2Array(-7, 12, -4, 14, 4, 14, 6, 12, 6, 7, 4, 5, -4, 5, -7, 7)

[node name="PlayerDetector" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="PlayerDetector"]
position = Vector2(0, 6)
shape = SubResource("CircleShape2D_3ayf4")
