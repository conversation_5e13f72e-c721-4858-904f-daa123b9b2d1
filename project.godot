; Engine configuration file.
; It's best edited using the editor UI and not directly,
; since the parameters that go here are not all obvious.
;
; Format:
;   [section] ; section goes between []
;   param=value ; assign values to parameters

config_version=5

[application]

config/name="crafterio"
run/main_scene="uid://dq0nxjiipwin0"
config/features=PackedStringArray("4.4", "C#", "Mobile")
config/icon="res://icon.svg"

[autoload]

ResourcesManager="*res://scenes/ResourcesManager.tscn"
TextureManager="*res://scenes/TextureManager.tscn"
CommonSignals="*res://scenes/CommonSignals.tscn"
LevelManager="*res://scenes/LevelManager.tscn"
PlayerStatsManager="*res://scenes/PlayerStatsManager.tscn"
MenuManager="*res://scenes/MenuManager.tscn"

[display]

window/size/viewport_width=1920
window/size/viewport_height=1080
window/size/mode=3
window/stretch/scale=2.5
mouse_cursor/custom_image="uid://drmnf2no6lg7c"

[dotnet]

project/assembly_name="crafterio"

[input]

Interact={
"deadzone": 0.2,
"events": [Object(InputEventKey,"resource_local_to_scene":false,"resource_name":"","device":0,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"pressed":false,"keycode":0,"physical_keycode":69,"key_label":0,"unicode":0,"location":0,"echo":false,"script":null)
]
}
UseTool={
"deadzone": 0.2,
"events": [Object(InputEventMouseButton,"resource_local_to_scene":false,"resource_name":"","device":-1,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"button_mask":0,"position":Vector2(0, 0),"global_position":Vector2(0, 0),"factor":1.0,"button_index":2,"canceled":false,"pressed":false,"double_click":false,"script":null)
]
}
Escape={
"deadzone": 0.2,
"events": [Object(InputEventKey,"resource_local_to_scene":false,"resource_name":"","device":0,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"pressed":false,"keycode":0,"physical_keycode":4194305,"key_label":0,"unicode":0,"location":0,"echo":false,"script":null)
]
}

[internationalization]

locale/translations=PackedStringArray("res://data/translations.de.translation", "res://data/translations.en.translation", "res://data/translations.es.translation", "res://data/translations.fr.translation", "res://data/translations.pl.translation")

[rendering]

textures/canvas_textures/default_texture_filter=0
renderer/rendering_method="mobile"
