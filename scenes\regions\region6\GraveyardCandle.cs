using Godot;

public partial class GraveyardCandle : Node2D
{
	[Export] public int CandleId { get; set; } = 1;
	
	private Area2D _playerDetector;
	private AnimationPlayer _animationPlayer;
	private bool _isPlayerInRange = false;
	private bool _isLit = false;
	private Node2D _keyE;
	private AnimationPlayer _keyAnimationPlayer;

	public override void _Ready()
	{
		_playerDetector = GetNode<Area2D>("PlayerDetector");
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		
		if (_playerDetector != null)
		{
			_playerDetector.AreaEntered += OnPlayerEntered;
			_playerDetector.AreaExited += OnPlayerExited;
		}
		else
		{
			GD.PrintErr("GraveyardCandle: PlayerDetector not found!");
		}

		if (_animationPlayer == null)
		{
			GD.PrintErr("GraveyardCandle: AnimationPlayer not found!");
		}

		// Find KeyE node in player
		var player = GetTree().GetFirstNodeInGroup("Player");
		if (player != null)
		{
			_keyE = player.GetNode<Node2D>("Keys/KeyE");
			if (_keyE != null)
			{
				_keyAnimationPlayer = _keyE.GetNode<AnimationPlayer>("AnimationPlayer");
			}
		}

		// Initially play Off animation
		if (_animationPlayer != null)
		{
			_animationPlayer.Play("Off");
		}
	}

	public override void _ExitTree()
	{
		if (_playerDetector != null)
		{
			_playerDetector.AreaEntered -= OnPlayerEntered;
			_playerDetector.AreaExited -= OnPlayerExited;
		}
	}

	public override void _Input(InputEvent @event)
	{
		if (!_isPlayerInRange || _isLit) return;

		if (@event.IsActionPressed("Interact"))
		{
			LightCandle();
			GetViewport().SetInputAsHandled();
		}
	}

	private void OnPlayerEntered(Area2D area)
	{
		if (area.Name == "PlayerDetector" && !_isLit)
		{
			_isPlayerInRange = true;
			ShowKeyPrompt();
		}
	}

	private void OnPlayerExited(Area2D area)
	{
		if (area.Name == "PlayerDetector")
		{
			_isPlayerInRange = false;
			HideKeyPrompt();
		}
	}

	private void ShowKeyPrompt()
	{
		if (_keyE != null && _keyAnimationPlayer != null)
		{
			_keyE.Visible = true;
			_keyAnimationPlayer.Play("PressKey");
		}
	}

	private void HideKeyPrompt()
	{
		if (_keyE != null)
		{
			_keyE.Visible = false;
		}
	}

	private void LightCandle()
	{
		if (_isLit) return;

		_isLit = true;
		HideKeyPrompt();

		// Play On animation
		if (_animationPlayer != null)
		{
			_animationPlayer.Play("On");
		}

		// Emit signal through CommonSignals
		CommonSignals.Instance?.EmitGraveyardCandleInteracted(CandleId);
		
		GD.Print($"GraveyardCandle: Candle {CandleId} lit!");
	}

	public void SetLit(bool lit)
	{
		_isLit = lit;
		if (_animationPlayer != null)
		{
			_animationPlayer.Play(lit ? "On" : "Off");
		}
		
		if (lit && _isPlayerInRange)
		{
			HideKeyPrompt();
		}
	}

	public bool IsLit()
	{
		return _isLit;
	}
}
