using Godot;
using System;
using System.IO;
using System.IO.Compression;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;

public static class SaveHandler
{
    private static readonly string SaveDirectory = "user://saves/";

    private static bool UseGzipCompression = false;

    private static readonly JsonSerializerOptions JsonOptions = new JsonSerializerOptions
    {
        WriteIndented = false,
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        Converters = { new JsonStringEnumConverter(), new Vector2IJsonConverter() },
        IncludeFields = true
    };

    static SaveHandler()
    {
        if (!DirAccess.DirExistsAbsolute(SaveDirectory))
        {
            DirAccess.Open("user://").MakeDir("saves");
        }
    }

    public static bool Save<T>(T data, string filename)
    {
        try
        {
            string jsonString = JsonSerializer.Serialize(data, JsonOptions);

            if (UseGzipCompression)
            {
                return SaveCompressed(jsonString, filename);
            }
            else
            {
                return SaveUncompressed(jsonString, filename);
            }
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Error saving data to {filename}: {ex.Message}");
            return false;
        }
    }

    private static bool SaveUncompressed(string jsonString, string filename)
    {
        try
        {
            string filePath = $"{SaveDirectory}{filename}.json";

            using var file = Godot.FileAccess.Open(filePath, Godot.FileAccess.ModeFlags.Write);
            if (file == null)
            {
                GD.PrintErr($"Failed to open file for writing: {filePath}");
                return false;
            }

            file.StoreString(jsonString);
            file.Close();

            GD.Print($"Saved uncompressed: {filePath} ({jsonString.Length} bytes)");
            return true;
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Error saving uncompressed data: {ex.Message}");
            return false;
        }
    }

    private static bool SaveCompressed(string jsonString, string filename)
    {
        try
        {
            string filePath = $"{SaveDirectory}{filename}.json.gz";

            byte[] jsonBytes = Encoding.UTF8.GetBytes(jsonString);
            byte[] compressedBytes;

            using (var memoryStream = new MemoryStream())
            {
                using (var gzipStream = new GZipStream(memoryStream, CompressionMode.Compress))
                {
                    gzipStream.Write(jsonBytes, 0, jsonBytes.Length);
                }
                compressedBytes = memoryStream.ToArray();
            }

            using var file = Godot.FileAccess.Open(filePath, Godot.FileAccess.ModeFlags.Write);
            if (file == null)
            {
                GD.PrintErr($"Failed to open file for writing: {filePath}");
                return false;
            }

            file.StoreBuffer(compressedBytes);
            file.Close();

            float compressionRatio = (float)compressedBytes.Length / jsonBytes.Length;
            GD.Print($"Saved compressed: {filePath} ({compressedBytes.Length} bytes, {compressionRatio:P1} of original)");
            return true;
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Error saving compressed data: {ex.Message}");
            return false;
        }
    }

    public static T Load<T>(string filename)
    {
        try
        {
            string compressedPath = $"{SaveDirectory}{filename}.json.gz";
            if (Godot.FileAccess.FileExists(compressedPath))
            {
                return LoadCompressed<T>(compressedPath);
            }

            string uncompressedPath = $"{SaveDirectory}{filename}.json";
            if (Godot.FileAccess.FileExists(uncompressedPath))
            {
                return LoadUncompressed<T>(uncompressedPath);
            }

            return default(T);
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Error loading data from {filename}: {ex.Message}");
            return default(T);
        }
    }

    private static T LoadUncompressed<T>(string filePath)
    {
        using var file = Godot.FileAccess.Open(filePath, Godot.FileAccess.ModeFlags.Read);
        if (file == null)
        {
            GD.PrintErr($"Failed to open file for reading: {filePath}");
            return default(T);
        }

        string jsonString = file.GetAsText();
        file.Close();

        T data = JsonSerializer.Deserialize<T>(jsonString, JsonOptions);
        GD.Print($"Loaded uncompressed: {filePath}");
        return data;
    }

    private static T LoadCompressed<T>(string filePath)
    {
        using var file = Godot.FileAccess.Open(filePath, Godot.FileAccess.ModeFlags.Read);
        if (file == null)
        {
            GD.PrintErr($"Failed to open file for reading: {filePath}");
            return default(T);
        }

        byte[] compressedBytes = file.GetBuffer((long)file.GetLength());
        file.Close();

        byte[] jsonBytes;
        using (var memoryStream = new MemoryStream(compressedBytes))
        {
            using (var gzipStream = new GZipStream(memoryStream, CompressionMode.Decompress))
            {
                using (var outputStream = new MemoryStream())
                {
                    gzipStream.CopyTo(outputStream);
                    jsonBytes = outputStream.ToArray();
                }
            }
        }

        string jsonString = Encoding.UTF8.GetString(jsonBytes);
        T data = JsonSerializer.Deserialize<T>(jsonString, JsonOptions);
        GD.Print($"Loaded compressed: {filePath}");
        return data;
    }

    public static bool SaveExists(string filename)
    {
        string compressedPath = $"{SaveDirectory}{filename}.json.gz";
        string uncompressedPath = $"{SaveDirectory}{filename}.json";
        return Godot.FileAccess.FileExists(compressedPath) || Godot.FileAccess.FileExists(uncompressedPath);
    }

    public static bool DeleteSave(string filename)
    {
        try
        {
            string filePath = $"{SaveDirectory}{filename}.json";

            if (!Godot.FileAccess.FileExists(filePath))
            {
                return false;
            }

            DirAccess.Open("user://").Remove(filePath);
            return true;
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Error deleting save file {filename}: {ex.Message}");
            return false;
        }
    }

    public static string[] GetAvailableSaves()
    {
        try
        {
            var dir = DirAccess.Open(SaveDirectory);
            if (dir == null)
                return new string[0];

            var saves = new System.Collections.Generic.List<string>();
            dir.ListDirBegin();

            string fileName = dir.GetNext();
            while (fileName != "")
            {
                if (fileName.EndsWith(".json"))
                {
                    saves.Add(fileName.Replace(".json", ""));
                }
                fileName = dir.GetNext();
            }

            return saves.ToArray();
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Error getting available saves: {ex.Message}");
            return new string[0];
        }
    }
}
